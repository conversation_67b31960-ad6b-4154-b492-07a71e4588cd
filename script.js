document.addEventListener('DOMContentLoaded', function() {
    // Check if there's any data
    if (dailyCounts.length === 0) {
        console.error("No subscriber data found!");
        return;
    }
    
    // Get the current total subscribers (most recent entry)
    const currentTotal = dailyCounts[0].total;
    
    // Calculate remaining subscribers to goal
    const remainingToGoal = subscriberGoal - currentTotal;
    
    // Calculate today's gained subscribers by comparing with previous day
    const todayGained = calculateTodayGained();
    
    // Calculate progress percentage
    const progressPercentage = (currentTotal / subscriberGoal) * 100;
    
    // Check if goal has been reached
    if (currentTotal >= subscriberGoal) {
        showGoalReached(currentTotal);
    } else {
        // Update the UI
        updateUI(currentTotal, remainingToGoal, todayGained, progressPercentage);
    }
    
    // Populate history with daily changes
    populateHistory();
    
    // Add structured data for better SEO
    addDynamicStructuredData(currentTotal, progressPercentage);
});

/**
 * Calculate how many subscribers were gained today
 * @returns {number} Today's subscriber gain
 */
function calculateTodayGained() {
    // If we have less than 2 days of data, we can't calculate the gain
    if (dailyCounts.length < 2) {
        return dailyCounts.length > 0 ? dailyCounts[0].total : 0;
    }
    
    // Calculate the difference between today and yesterday
    const todayCount = dailyCounts[0].total;
    const yesterdayCount = dailyCounts[1].total;
    
    return todayCount - yesterdayCount;
}

/**
 * Calculate the daily subscriber gains for all recorded days
 * @returns {Array} Array of daily gains objects with date and gain
 */
function calculateDailyGains() {
    const gains = [];
    
    // Process each day
    for (let i = 0; i < dailyCounts.length; i++) {
        const currentDay = dailyCounts[i];
        
        // For the first entry, the gain is the same as the total (assuming we started from 0)
        if (i === dailyCounts.length - 1) {
            gains.push({
                date: currentDay.date,
                gained: currentDay.total
            });
        } else {
            // Otherwise, calculate the difference between this day and the next day
            const nextDay = dailyCounts[i + 1];
            const gain = currentDay.total - nextDay.total;
            gains.push({
                date: currentDay.date,
                gained: gain
            });
        }
    }
    
    return gains;
}

/**
 * Update the UI with the calculated values
 * @param {number} total - Total subscribers
 * @param {number} remaining - Remaining subscribers to goal
 * @param {number} today - Today's subscribers gained
 * @param {number} percentage - Progress percentage
 */
function updateUI(total, remaining, today, percentage) {
    // Update the subscribers-to-go counter
    const subscribersToGoElement = document.getElementById('subscribers-to-go');
    animateCounter(subscribersToGoElement, remaining);
    
    // Update the current subscribers counter
    const currentSubsElement = document.getElementById('current-subs');
    currentSubsElement.textContent = formatNumber(total);
    
    // Update today's subscribers
    const todaySubsElement = document.getElementById('today-subs');
    todaySubsElement.textContent = today;
    
    // Update progress bar and its ARIA attributes
    const progressBarElement = document.getElementById('progress-bar');
    progressBarElement.style.width = Math.min(percentage, 100) + '%';
    progressBarElement.setAttribute('aria-valuenow', Math.round(percentage));
    progressBarElement.setAttribute('aria-valuetext', `${Math.round(percentage)}% to goal`);
    
    // Update page title with current stats for better SEO
    document.title = `${formatNumber(remaining)} to go - Shushie Valorant | Current: ${formatNumber(total)} Subscribers`;
    
    // Update meta description with current stats
    updateMetaDescription(total, remaining);
}

/**
 * Populate the history list
 */
function populateHistory() {
    const historyListElement = document.getElementById('history-list');
    
    // Clear existing entries
    historyListElement.innerHTML = '';
    
    // Calculate daily gains from the total counts
    const dailyGains = calculateDailyGains();
    
    // Show at most the last 10 entries
    const entriesToShow = dailyGains.slice(0, 10);
    
    entriesToShow.forEach(day => {
        const listItem = document.createElement('li');
        
        const dateSpan = document.createElement('span');
        dateSpan.innerHTML = `<i class="far fa-calendar-alt" aria-hidden="true"></i> ${formatDate(day.date)}`;
        
        const gainSpan = document.createElement('span');
        const gainDirection = day.gained > 0 ? 'increase' : (day.gained < 0 ? 'decrease' : 'no change');
        const gainIcon = day.gained > 0 ? '<i class="fas fa-caret-up" aria-hidden="true"></i>' : '<i class="fas fa-caret-down" aria-hidden="true"></i>';
        
        gainSpan.innerHTML = `${gainIcon} ${Math.abs(day.gained)} subscribers`;
        gainSpan.setAttribute('aria-label', `${Math.abs(day.gained)} subscribers ${gainDirection} on ${formatDateForScreenReader(day.date)}`);
        
        listItem.appendChild(dateSpan);
        listItem.appendChild(gainSpan);
        historyListElement.appendChild(listItem);
    });
}

/**
 * Format a number with thousands separators
 * @param {number} num - Number to format
 * @returns {string} Formatted number
 */
function formatNumber(num) {
    return num.toLocaleString();
}

/**
 * Format a date string as a more readable format
 * @param {string} dateString - Date in YYYY-MM-DD format
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

/**
 * Format a date string for screen readers
 * @param {string} dateString - Date in YYYY-MM-DD format
 * @returns {string} Formatted date for screen readers
 */
function formatDateForScreenReader(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

/**
 * Animate a counter from its current value to a target value
 * @param {HTMLElement} element - Element to animate
 * @param {number} target - Target number
 */
function animateCounter(element, target) {
    // Parse the current value
    const currentValue = parseInt(element.textContent.replace(/,/g, ''));
    
    // Calculate the step for animation
    const duration = 1000; // 1 second
    const steps = 60;
    const stepValue = (target - currentValue) / steps;
    
    let current = currentValue;
    const timer = setInterval(() => {
        current += stepValue;
        
        // Check if we've reached the target
        if ((stepValue > 0 && current >= target) || 
            (stepValue < 0 && current <= target)) {
            clearInterval(timer);
            element.textContent = formatNumber(target);
        } else {
            element.textContent = formatNumber(Math.round(current));
        }
    }, duration / steps);
}

/**
 * Display celebration when goal is reached
 * @param {number} total - Total subscribers
 */
function showGoalReached(total) {
    // Get container
    const container = document.querySelector('.container');
    
    // Clear the container
    container.innerHTML = '';
    
    // Create celebration content
    const celebration = document.createElement('div');
    celebration.className = 'celebration';
    celebration.setAttribute('role', 'status');
    celebration.setAttribute('aria-live', 'polite');
    
    celebration.innerHTML = `
        <h1>GOAL REACHED!</h1>
        <div class="celebration-count">${formatNumber(total)}</div>
        <div class="celebration-text">SUBSCRIBERS</div>
        <div class="fireworks" aria-hidden="true">
            <div class="firework"></div>
            <div class="firework"></div>
            <div class="firework"></div>
        </div>
        <div class="celebration-message">
            Thank you for the support!<br>
            Road to the next milestone begins...
        </div>
        <a href="https://www.youtube.com/@Shushie_valorant?sub_confirmation=1" target="_blank" rel="noopener" class="celebration-subscribe-button" aria-label="Subscribe to Shushie Valorant on YouTube">
            <i class="fab fa-youtube" aria-hidden="true"></i> Subscribe Now
        </a>
    `;
    
    // Add to container
    container.appendChild(celebration);
    
    // Update page title
    document.title = `Goal Reached! ${formatNumber(total)} Subscribers - Shushie Valorant`;
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.setAttribute('content', `Shushie Valorant has reached the amazing milestone of ${formatNumber(total)} subscribers! Thank you for all the support on this gaming journey.`);
    }
    
    // Add celebration styles
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .celebration {
            text-align: center;
            padding: 2rem;
            animation: pulse 2s infinite;
        }
        
        .celebration-count {
            font-size: 5rem;
            font-weight: bold;
            color: var(--valorant-red);
            margin: 2rem 0;
            text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
        }
        
        .celebration-text {
            font-size: 2rem;
            letter-spacing: 5px;
            margin-bottom: 2rem;
        }
        
        .celebration-message {
            font-size: 1.5rem;
            margin-top: 2rem;
            line-height: 1.5;
        }
        
        .celebration-subscribe-button {
            display: inline-block;
            background-color: var(--valorant-red);
            color: var(--valorant-light);
            padding: 15px 30px;
            font-size: 1.3rem;
            font-weight: 600;
            text-transform: uppercase;
            text-decoration: none;
            margin-top: 2rem;
            border-radius: 4px;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(255, 70, 85, 0.4);
        }
        
        .celebration-subscribe-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(255, 70, 85, 0.5);
        }
        
        .celebration-subscribe-button:focus {
            outline: 2px solid rgba(255, 255, 255, 0.5);
            outline-offset: 3px;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .fireworks {
            position: relative;
            height: 200px;
        }
        
        .firework {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            box-shadow: 0 0 20px 10px var(--valorant-red);
            animation: explode 1s infinite;
        }
        
        .firework:nth-child(1) {
            left: 30%;
            animation-delay: 0.2s;
        }
        
        .firework:nth-child(2) {
            left: 50%;
            animation-delay: 0.5s;
        }
        
        .firework:nth-child(3) {
            left: 70%;
            animation-delay: 0.8s;
        }
        
        @keyframes explode {
            0% { transform: scale(0); opacity: 1; }
            100% { transform: scale(3); opacity: 0; }
        }
    `;
    
    document.head.appendChild(styleElement);
}

/**
 * Update meta description with current subscriber stats
 * @param {number} total - Total subscribers
 * @param {number} remaining - Remaining subscribers to goal
 */
function updateMetaDescription(total, remaining) {
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.setAttribute('content', `Follow Shushie's journey to 100K YouTube subscribers. Currently at ${formatNumber(total)} subscribers with ${formatNumber(remaining)} more to go! Watch Valorant gaming content, tips, and gameplay.`);
    }
}

/**
 * Add dynamic structured data based on current subscriber stats
 * @param {number} total - Total subscribers
 * @param {number} percentage - Progress percentage
 */
function addDynamicStructuredData(total, percentage) {
    // Create dynamic schema
    const progressSchema = {
        "@context": "https://schema.org",
        "@type": "InteractionCounter",
        "interactionType": {
            "@type": "http://schema.org/SubscribeAction"
        },
        "userInteractionCount": total,
        "name": "YouTube Subscribers"
    };
    
    // Add to page
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(progressSchema);
    document.head.appendChild(script);
} 