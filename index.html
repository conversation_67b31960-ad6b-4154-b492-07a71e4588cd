<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Primary Meta Tags -->
    <title>Shushie Valorant - Road to 100K YouTube Subscribers | Valorant Gaming Content</title>
    <meta name="title" content="Shushie Valorant - Road to 100K YouTube Subscribers | Valorant Gaming Content">
    <meta name="description" content="Follow <PERSON><PERSON><PERSON>'s journey to 100K YouTube subscribers. Watch high-quality Valorant gaming content, tips, tricks and gameplay from a dedicated content creator.">
    <meta name="keywords" content="Shushie Valorant, Valorant YouTube, Valorant gameplay, Valorant tips, gaming channel, subscriber count, Valorant content creator">
    <meta name="author" content="Shushie Valorant">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.youtube.com/@Shushie_valorant">
    <meta property="og:title" content="Shushie Valorant - Road to 100K YouTube Subscribers | Valorant Gaming Content">
    <meta property="og:description" content="Follow Shushi<PERSON>'s journey to 100K YouTube subscribers. Watch high-quality Valorant gaming content, tips, tricks and gameplay from a dedicated content creator.">
    <meta property="og:image" content="channels4_profile.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.youtube.com/@Shushie_valorant">
    <meta property="twitter:title" content="Shushie Valorant - Road to 100K YouTube Subscribers | Valorant Gaming Content">
    <meta property="twitter:description" content="Follow Shushie's journey to 100K YouTube subscribers. Watch high-quality Valorant gaming content, tips, tricks and gameplay from a dedicated content creator.">
    <meta property="twitter:image" content="channels4_profile.jpg">
    
    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    
    <!-- Styles -->
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.youtube.com/@Shushie_valorant">
</head>
<body>
    <header>
        <h1>Road to 100K</h1>
    </header>
    
    <main class="container">
        <section class="channel-section">
            <div class="channel-info" aria-label="Channel Information">
                <img src="channels4_profile.jpg" alt="Shushie Valorant YouTube Channel Logo - Valorant Gaming Content Creator" class="channel-logo">
                <div class="channel-name">
                    <a href="https://www.youtube.com/@Shushie_valorant" target="_blank" rel="noopener" aria-label="Visit Shushie Valorant YouTube Channel">@Shushie_valorant</a>
                </div>
            </div>
            
            <a href="https://www.youtube.com/@Shushie_valorant?sub_confirmation=1" target="_blank" rel="noopener" class="subscribe-button" aria-label="Subscribe to Shushie Valorant on YouTube">
                <i class="fab fa-youtube" aria-hidden="true"></i> Subscribe
            </a>
        </section>
        
        <section class="stats-section" aria-label="Subscriber Statistics">
            <div class="counter">
                <div class="counter-value" id="subscribers-to-go">100000</div>
                <div class="counter-label">subscribers to go</div>
            </div>

            <div class="stats">
                <div class="stat">
                    <div class="stat-value" id="current-subs">0</div>
                    <div class="stat-label">Current Subscribers</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="today-subs">0</div>
                    <div class="stat-label">Subscribers Today</div>
                </div>
            </div>

            <div class="progress-container" aria-label="Subscriber goal progress">
                <div class="progress-bar" id="progress-bar" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"></div>
            </div>
        </section>

        <section class="history-section" aria-label="Subscriber History">
            <div class="recent-history">
                <h2><i class="fas fa-chart-line" aria-hidden="true"></i> Recent Growth</h2>
                <ul id="history-list"></ul>
            </div>
        </section>
    </main>

    <footer>
        <p>Valorant Content Creator | <i class="fas fa-code" aria-hidden="true"></i> Subscriber Goal Tracker</p>
    </footer>

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Shushie Valorant",
        "url": "https://www.youtube.com/@Shushie_valorant",
        "image": "channels4_profile.jpg",
        "sameAs": [
            "https://www.youtube.com/@Shushie_valorant"
        ],
        "description": "Valorant gaming content creator tracking the journey to 100K YouTube subscribers."
    }
    </script>
    
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Shushie Valorant - Road to 100K Subscribers",
        "description": "Track Shushie Valorant's journey to 100K YouTube subscribers with real-time subscriber count updates.",
        "publisher": {
            "@type": "Person",
            "name": "Shushie Valorant"
        }
    }
    </script>

    <!-- Scripts -->
    <script src="subscriber-data.js" defer></script>
    <script src="script.js" defer></script>
</body>
</html> 