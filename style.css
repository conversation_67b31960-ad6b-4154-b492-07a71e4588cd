@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap');

:root {
    --valorant-red: #ff4655;
    --valorant-dark: #0f1923;
    --valorant-light: #ece8e1;
    --valorant-accent: #bd3944;
    --valorant-gray: #383e3a;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Rajdhani', sans-serif;
}

body {
    background-color: var(--valorant-dark);
    color: var(--valorant-light);
    line-height: 1.6;
    min-height: 100vh;
    background-image: linear-gradient(135deg, rgba(15, 25, 35, 0.97) 0%, rgba(30, 38, 46, 0.97) 100%);
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    padding: 2rem 1rem 0.5rem;
    text-align: center;
}

h1 {
    font-size: 3.5rem;
    margin-bottom: 2rem;
    color: var(--valorant-red);
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 700;
    position: relative;
    text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.3);
    display: inline-block;
}

h1::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -10px;
    transform: translateX(-50%);
    width: 120px;
    height: 3px;
    background: var(--valorant-red);
}

/* Main Content */
main.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 1.5rem 2rem 2rem;
    text-align: center;
    flex: 1;
}

/* Channel Section */
.channel-section {
    margin-bottom: 2.5rem;
}

.channel-info {
    margin-top: 1rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.channel-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
    border: 2px solid var(--valorant-red);
    object-fit: cover;
}

.channel-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--valorant-light);
}

.channel-name a {
    color: var(--valorant-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.channel-name a:hover {
    color: var(--valorant-red);
}

.subscribe-button {
    display: inline-block;
    background-color: var(--valorant-red);
    color: var(--valorant-light);
    padding: 14px 28px;
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    text-decoration: none;
    margin-bottom: 2rem;
    border-radius: 4px;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.4);
}

.subscribe-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: all 0.6s ease;
}

.subscribe-button:hover:before {
    left: 100%;
}

.subscribe-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 20px rgba(255, 70, 85, 0.5);
}

.subscribe-button:active {
    transform: translateY(0);
    box-shadow: 0 4px 10px rgba(255, 70, 85, 0.4);
}

.subscribe-button:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 3px;
}

.subscribe-button i {
    margin-right: 8px;
    font-size: 1.2rem;
}

/* Stats Section */
.counter {
    background-color: rgba(15, 25, 35, 0.8);
    border-radius: 4px;
    padding: 3rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    margin-bottom: 2.5rem;
    border-left: 4px solid var(--valorant-red);
    position: relative;
    overflow: hidden;
}

.counter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 65%, rgba(255, 70, 85, 0.1) 75%, transparent 85%);
    z-index: -1;
}

.counter-value {
    font-size: 5rem;
    font-weight: 700;
    color: var(--valorant-red);
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
    font-family: 'Rajdhani', sans-serif;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.counter-label {
    font-size: 1.4rem;
    text-transform: uppercase;
    letter-spacing: 3px;
    color: var(--valorant-light);
    opacity: 0.8;
}

.stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 2.5rem;
    gap: 2rem;
}

.stat {
    background-color: rgba(15, 25, 35, 0.8);
    border-radius: 4px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    flex-basis: 48%;
    position: relative;
    overflow: hidden;
    border-bottom: 3px solid var(--valorant-red);
}

.stat::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: var(--valorant-red);
    clip-path: polygon(100% 0, 0 0, 100% 100%);
}

.stat-value {
    font-size: 3rem;
    font-weight: 700;
    color: var(--valorant-light);
    margin-bottom: 0.3rem;
}

.stat-label {
    font-size: 1rem;
    color: var(--valorant-light);
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.7;
}

.progress-container {
    background-color: rgba(56, 62, 58, 0.3);
    height: 30px;
    border-radius: 2px;
    margin-bottom: 3rem;
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(255, 70, 85, 0.3);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(to right, var(--valorant-red), var(--valorant-accent));
    border-radius: 2px;
    width: 0%;
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    animation: shine 2s infinite;
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* History Section */
.history-section {
    margin-bottom: 2rem;
}

.recent-history {
    background-color: rgba(15, 25, 35, 0.8);
    border-radius: 4px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 2.5rem;
    border-top: 3px solid var(--valorant-red);
}

.recent-history h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: var(--valorant-light);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
}

.recent-history h2::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background: var(--valorant-red);
}

#history-list {
    list-style: none;
    text-align: left;
    max-height: 280px;
    overflow-y: auto;
    padding-right: 10px;
    font-size: 1.1rem;
}

#history-list::-webkit-scrollbar {
    width: 6px;
    background-color: rgba(15, 25, 35, 0.8);
}

#history-list::-webkit-scrollbar-thumb {
    background-color: var(--valorant-red);
    border-radius: 2px;
}

#history-list li {
    padding: 0.9rem 0.7rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#history-list li:last-child {
    border-bottom: none;
}

#history-list li span:last-child {
    color: var(--valorant-red);
    font-weight: 600;
}

/* Footer */
footer {
    text-align: center;
    padding: 1rem;
    color: var(--valorant-light);
    font-size: 0.9rem;
    opacity: 0.7;
    margin-top: auto;
}

/* Media Queries */
@media (max-width: 600px) {
    h1 {
        font-size: 2rem;
    }
    
    .stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .counter-value {
        font-size: 3rem;
    }
    
    main.container {
        padding: 1rem;
    }
}

/* Accessibility */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
} 