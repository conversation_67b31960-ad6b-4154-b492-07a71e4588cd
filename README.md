# YouTube Subscriber Goal Tracker

A simple static website to track progress towards 100K YouTube subscribers. The tracker shows:

- How many subscribers are left until you reach 100K
- Your current subscriber count
- Today's subscriber gains
- A progress bar showing your journey to 100K
- Recent history of your subscriber growth

## How to Use

### Setting Up

1. Clone this repository to your local machine
2. Open it with your preferred code editor
3. Update your subscriber data (see below)
4. Deploy to GitHub Pages or any static web host

### Updating Your Subscriber Data

To update your subscriber count:

1. Open the `subscriber-data.js` file
2. Add new entries to the `dailyCounts` array at the top in this format:
   ```javascript
   { date: "YYYY-MM-DD", total: X },
   ```
3. The most recent day should always be at the top of the list
4. Commit and push your changes

The system will automatically calculate how many subscribers you gained each day!

### Example Update

```javascript
// Your subscriber goal
const subscriberGoal = 100000;

// Daily subscriber counts - add a new entry each day
const dailyCounts = [
    { date: "2023-07-02", total: 5027 }, // Added today
    { date: "2023-07-01", total: 5015 },
    { date: "2023-06-30", total: 5010 },
];
```

In this example, you gained 12 subscribers on July 2nd and 5 subscribers on July 1st.

## Customization

You can customize the appearance by editing the `style.css` file. Change colors, fonts, and layout to match your brand.

## Viewing Locally

Simply open the `index.html` file in your web browser to see the tracker in action.

## Deployment

For the simplest deployment, use GitHub Pages:

1. Push your repository to GitHub
2. Go to repository Settings > Pages
3. Select main branch as the source
4. Your site will be available at `https://[username].github.io/[repo-name]/`

## Technical Details

This is a fully static site using only HTML, CSS, and JavaScript. No server-side processing or APIs are needed, making it easy to host anywhere. 